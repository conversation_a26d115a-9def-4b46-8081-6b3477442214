#!/usr/bin/env python3
"""
Script di pulizia quota EMERGENZA - Per quando la quota è VERAMENTE piena
ATTENZIONE: Più aggressivo ma sempre sicuro per i checkpoint attivi
"""

import os
import shutil
import glob
import logging
from datetime import datetime, timedelta
import subprocess

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# CONFIGURAZIONE SICUREZZA EMERGENZA
PROTECTED_CHECKPOINT_DIRS = [
    "experiments/xml_direct_input/outputs/gemma_t9_no_accumulation",
    "experiments/xml_direct_input/outputs/llama_t8_24h", 
    "experiments/xml_direct_input/outputs/llama_t9_no_accumulation"
]

ACTIVE_JOB_IDS = ["2600849", "2600850", "2600851"]

def get_directory_size(path):
    """Calcola dimensione directory in GB"""
    try:
        result = subprocess.run(['du', '-sb', path], capture_output=True, text=True)
        if result.returncode == 0:
            bytes_size = int(result.stdout.split()[0])
            return bytes_size / (1024**3)
        return 0
    except:
        return 0

def emergency_clean_old_checkpoints():
    """EMERGENZA: Mantiene solo ultimi 2 checkpoint per modello (come da memoria)"""
    logger.info("🚨 EMERGENZA: Pulizia checkpoint vecchi (mantiene ultimi 2)...")
    
    cleaned_size = 0
    
    for checkpoint_dir in PROTECTED_CHECKPOINT_DIRS:
        full_path = f"/work/tesi_ediluzio/{checkpoint_dir}"
        if not os.path.exists(full_path):
            continue
            
        # Trova tutti i checkpoint
        checkpoints = glob.glob(os.path.join(full_path, "checkpoint-*"))
        if len(checkpoints) <= 2:
            logger.info(f"✅ {os.path.basename(checkpoint_dir)}: Solo {len(checkpoints)} checkpoint, nessuna pulizia")
            continue
        
        # Ordina per numero di step (più vecchi prima)
        def extract_step(path):
            try:
                return int(os.path.basename(path).split('-')[1])
            except:
                return 0
        
        checkpoints.sort(key=extract_step)
        
        # Elimina tutti tranne gli ultimi 2
        checkpoints_to_delete = checkpoints[:-2]
        
        for checkpoint in checkpoints_to_delete:
            try:
                size = get_directory_size(checkpoint) * (1024**3)
                shutil.rmtree(checkpoint)
                cleaned_size += size
                step = os.path.basename(checkpoint)
                logger.info(f"🗑️ EMERGENZA: Eliminato {os.path.basename(checkpoint_dir)}/{step}")
            except Exception as e:
                logger.warning(f"⚠️ Errore eliminazione checkpoint {checkpoint}: {e}")
    
    return cleaned_size

def emergency_clean_evaluation_results():
    """EMERGENZA: Pulisce risultati evaluation vecchi (mantiene baseline)"""
    logger.info("🚨 EMERGENZA: Pulizia risultati evaluation vecchi...")
    
    eval_dir = "/work/tesi_ediluzio/evaluation_results"
    if not os.path.exists(eval_dir):
        return 0
    
    cleaned_size = 0
    
    # Mantiene solo file baseline e trained_models più recenti
    all_files = glob.glob(os.path.join(eval_dir, "*"))
    
    # File da mantenere sempre
    protected_patterns = ["baseline_", "trained_models_"]
    
    for file_path in all_files:
        filename = os.path.basename(file_path)
        
        # Controlla se è protetto
        is_protected = any(pattern in filename for pattern in protected_patterns)
        
        if not is_protected:
            try:
                if os.path.isfile(file_path):
                    size = os.path.getsize(file_path)
                    os.remove(file_path)
                    cleaned_size += size
                    logger.info(f"🗑️ EMERGENZA: Eliminato evaluation file: {filename}")
                elif os.path.isdir(file_path):
                    size = get_directory_size(file_path) * (1024**3)
                    shutil.rmtree(file_path)
                    cleaned_size += size
                    logger.info(f"🗑️ EMERGENZA: Eliminata evaluation dir: {filename}")
            except Exception as e:
                logger.warning(f"⚠️ Errore eliminazione {file_path}: {e}")
    
    return cleaned_size

def emergency_clean_data_cache():
    """EMERGENZA: Pulisce cache dati non essenziali"""
    logger.info("🚨 EMERGENZA: Pulizia cache dati...")
    
    cleaned_size = 0
    
    # Pulisce cache in data/ (mantiene file principali)
    data_dir = "/work/tesi_ediluzio/data"
    if os.path.exists(data_dir):
        cache_patterns = ["**/cache", "**/.cache", "**/tmp", "**/temp"]
        
        for pattern in cache_patterns:
            cache_files = glob.glob(os.path.join(data_dir, pattern), recursive=True)
            for cache_file in cache_files:
                try:
                    if os.path.isdir(cache_file):
                        size = get_directory_size(cache_file) * (1024**3)
                        shutil.rmtree(cache_file)
                        cleaned_size += size
                        logger.info(f"🗑️ EMERGENZA: Eliminata cache dati: {os.path.basename(cache_file)}")
                except Exception as e:
                    logger.warning(f"⚠️ Errore eliminazione cache dati {cache_file}: {e}")
    
    return cleaned_size

def emergency_clean_all_wandb():
    """EMERGENZA: Pulisce TUTTE le run WandB tranne l'ultima"""
    logger.info("🚨 EMERGENZA: Pulizia massiva WandB...")
    
    wandb_dir = "/work/tesi_ediluzio/wandb"
    if not os.path.exists(wandb_dir):
        return 0
    
    cleaned_size = 0
    
    # Trova tutte le run
    run_dirs = glob.glob(os.path.join(wandb_dir, "run-*"))
    if len(run_dirs) <= 1:
        logger.info("✅ Solo 1 run WandB, nessuna pulizia")
        return 0
    
    # Ordina per data di modifica (più vecchie prime)
    run_dirs.sort(key=lambda x: os.path.getmtime(x))
    
    # Elimina tutte tranne l'ultima
    runs_to_delete = run_dirs[:-1]
    
    for run_dir in runs_to_delete:
        try:
            size = get_directory_size(run_dir) * (1024**3)
            shutil.rmtree(run_dir)
            cleaned_size += size
            logger.info(f"🗑️ EMERGENZA: Eliminata run WandB: {os.path.basename(run_dir)}")
        except Exception as e:
            logger.warning(f"⚠️ Errore eliminazione run {run_dir}: {e}")
    
    return cleaned_size

def verify_critical_files():
    """Verifica che i file critici esistano ancora"""
    logger.info("🛡️ Verifica file critici...")
    
    critical_files = [
        "generate_trained_models_report.py",
        "generate_report.sh", 
        "clean_quota_safe.py",
        "params.md",
        "MEMORY_TRAINING_STATUS.md"
    ]
    
    for file_name in critical_files:
        file_path = f"/work/tesi_ediluzio/{file_name}"
        if os.path.exists(file_path):
            logger.info(f"✅ File critico OK: {file_name}")
        else:
            logger.warning(f"⚠️ File critico MANCANTE: {file_name}")
    
    # Verifica checkpoint
    for checkpoint_dir in PROTECTED_CHECKPOINT_DIRS:
        full_path = f"/work/tesi_ediluzio/{checkpoint_dir}"
        if os.path.exists(full_path):
            checkpoints = glob.glob(os.path.join(full_path, "checkpoint-*"))
            logger.info(f"✅ {os.path.basename(checkpoint_dir)}: {len(checkpoints)} checkpoint")
        else:
            logger.warning(f"⚠️ Directory checkpoint MANCANTE: {checkpoint_dir}")

def main():
    print("🚨 PULIZIA QUOTA EMERGENZA")
    print("=" * 50)
    print("⚠️ ATTENZIONE: Pulizia più aggressiva!")
    print("🛡️ REGOLE DI SICUREZZA EMERGENZA:")
    print("✅ MANTIENE: Ultimi 2 checkpoint per modello")
    print("✅ MANTIENE: File critici e script")
    print("✅ MANTIENE: Dataset principali")
    print("❌ ELIMINA: Checkpoint vecchi, cache, evaluation vecchi")
    print("=" * 50)
    
    # Conferma utente
    response = input("🚨 CONTINUARE CON PULIZIA EMERGENZA? (digita 'EMERGENZA' per confermare): ")
    if response != "EMERGENZA":
        print("❌ Pulizia annullata")
        return
    
    # Spazio iniziale
    initial_size = get_directory_size("/work/tesi_ediluzio")
    logger.info(f"📊 Spazio iniziale: {initial_size:.2f}GB")
    
    # Verifica file critici
    verify_critical_files()
    
    total_cleaned = 0
    
    # Pulizie emergenza
    total_cleaned += emergency_clean_old_checkpoints()
    total_cleaned += emergency_clean_evaluation_results()
    total_cleaned += emergency_clean_data_cache()
    total_cleaned += emergency_clean_all_wandb()
    
    # Pulizie standard
    from clean_quota_safe import (clean_huggingface_cache, clean_logs_old, 
                                  clean_home_cache, clean_python_cache)
    total_cleaned += clean_huggingface_cache()
    total_cleaned += clean_logs_old()
    total_cleaned += clean_home_cache()
    total_cleaned += clean_python_cache()
    
    # Spazio finale
    final_size = get_directory_size("/work/tesi_ediluzio")
    actual_freed = (initial_size - final_size) * (1024**3)
    
    print("\n" + "=" * 50)
    print("🚨 PULIZIA EMERGENZA COMPLETATA!")
    print("=" * 50)
    print(f"📊 Spazio iniziale: {initial_size:.2f}GB")
    print(f"📊 Spazio finale: {final_size:.2f}GB")
    print(f"🗑️ Spazio liberato: {actual_freed/(1024**3):.2f}GB")
    print(f"✅ File critici: PROTETTI")
    print(f"✅ Ultimi checkpoint: MANTENUTI")
    print("=" * 50)
    
    # Verifica finale
    verify_critical_files()

if __name__ == "__main__":
    main()
