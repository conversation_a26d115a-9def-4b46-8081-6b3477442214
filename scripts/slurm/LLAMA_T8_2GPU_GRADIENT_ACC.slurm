#!/bin/bash
#SBATCH --job-name=LLAMA_T8_2GPU_GRADIENT_ACC
#SBATCH --account=tesi_ediluzio
#SBATCH --output=logs/LLAMA_T8_2GPU_GRADIENT_ACC_%j.out
#SBATCH --error=logs/LLAMA_T8_2GPU_GRADIENT_ACC_%j.err
#SBATCH --partition=boost_usr_prod
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --gres=gpu:2
#SBATCH --mem=64G
#SBATCH --cpus-per-task=16
#SBATCH --qos=normal
#SBATCH --time=24:00:00

echo "🚀 LLAMA T8 - 2 GPU + GRADIENT ACCUMULATION"
echo "Job ID: $SLURM_JOB_ID"
echo "Node: $SLURMD_NODENAME"
echo "Start time: $(date)"
echo "🔄 RESUME: checkpoint-10250 (20.5% completato)"
echo "🎯 Configurazione: 2 GPU + Gradient Accumulation = 8"
echo "🎯 LoRA: Rank 64, Alpha 128"
echo "🎯 Quantization: DISABLED (--disable_quantization)"

# Environment setup
cd /work/tesi_ediluzio
source $(conda info --base)/etc/profile.d/conda.sh
conda activate svg_env_new

# Configurazione HuggingFace e CUDA
export HF_TOKEN="*************************************"
export HUGGINGFACE_HUB_TOKEN="*************************************"
export PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True,max_split_size_mb:512
export CUDA_LAUNCH_BLOCKING=1
export WANDB_PROJECT=svg_captioning
export WANDB_RUN_NAME=llama_t8_2gpu_gradient_acc
export TOKENIZERS_PARALLELISM=false
export HF_HOME=/work/tesi_ediluzio/.cache/huggingface

# Output directory
OUTPUT_DIR="experiments/xml_direct_input/outputs/llama_t8_2gpu_gradient_acc"

echo "✅ Python: $(which python)"
echo "✅ CUDA available: $(python -c 'import torch; print(torch.cuda.is_available())')"
echo "✅ GPU count: $(python -c 'import torch; print(torch.cuda.device_count())')"

echo "✅ Avvio LLAMA T8 2GPU + GRADIENT ACCUMULATION..."
echo "📁 Output: $OUTPUT_DIR"
echo "🔧 Config: experiments/xml_direct_input/configs/llama_t8_final_optimized.json"
echo "📊 Dataset: train_set_corrected_90k.json"
echo "🔄 RESUME: checkpoint-10250"
echo "🚀 2 GPU PARALLEL + GRADIENT ACCUMULATION"

# Crea directory output
mkdir -p "$OUTPUT_DIR"

# Training con 2 GPU + gradient accumulation + RESUME
/homes/ediluzio/.conda/envs/svg_env_new/bin/python -m torch.distributed.run \
    --nproc_per_node=2 \
    --master_port=29501 \
    scripts/training/train_lora_multi_gpu_simple.py \
    --model_name_or_path meta-llama/Llama-3.1-8B-Instruct \
    --data_file data/processed/xml_format_optimized/train_set_corrected_90k.json \
    --config_path experiments/xml_direct_input/configs/llama_t8_final_optimized.json \
    --output_dir "$OUTPUT_DIR" \
    --disable_quantization \
    --use_wandb \
    --wandb_project svg_captioning \
    --wandb_run_name llama_t8_2gpu_gradient_acc \
    --resume_from_checkpoint "experiments/xml_direct_input/outputs/llama_t8_24h/checkpoint-10250"

echo "🏁 LLAMA T8 2GPU + GRADIENT ACCUMULATION COMPLETATO"
echo "End time: $(date)"
echo "📁 Checkpoint salvati in: $OUTPUT_DIR"
