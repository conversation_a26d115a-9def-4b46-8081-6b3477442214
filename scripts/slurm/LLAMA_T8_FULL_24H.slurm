#!/bin/bash
#SBATCH --job-name=LLAMA_T8_FULL_24H
#SBATCH --account=tesi_ediluzio
#SBATCH --output=logs/LLAMA_T8_FULL_24H_%j.out
#SBATCH --error=logs/LLAMA_T8_FULL_24H_%j.err
#SBATCH --partition=boost_usr_prod
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --gres=gpu:1
#SBATCH --mem=32G
#SBATCH --cpus-per-task=8
#SBATCH --qos=normal
#SBATCH --time=24:00:00

echo "🚀 LLAMA T8 TRAINING COMPLETO 24H - L<PERSON><PERSON><PERSON>DO"
echo "Job ID: $SLURM_JOB_ID"
echo "Node: $SLURMD_NODENAME"
echo "Start time: $(date)"
echo "🔄 RESUME: checkpoint-7750 (TESTATO E FUNZIONANTE)"

cd /work/tesi_ediluzio
source $(conda info --base)/etc/profile.d/conda.sh
conda activate svg_env_new

# Configurazione IDENTICA al test che ha funzionato
export HF_TOKEN="*************************************"
export HUGGINGFACE_HUB_TOKEN="*************************************"
export PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True,max_split_size_mb:512
export CUDA_LAUNCH_BLOCKING=1
export WANDB_PROJECT=svg_captioning
export WANDB_RUN_NAME=llama_t8_full_24h_leonardo
export TOKENIZERS_PARALLELISM=false
export HF_HOME=/work/tesi_ediluzio/.cache/huggingface

OUTPUT_DIR="experiments/xml_direct_input/outputs/llama_t8_24h"

echo "✅ Python: $(which python)"
echo "✅ CUDA available: $(python -c 'import torch; print(torch.cuda.is_available())')"
echo "✅ Avvio LLAMA T8 TRAINING COMPLETO 24H..."
echo "📁 Output: $OUTPUT_DIR"
echo "🔧 Config: experiments/xml_direct_input/configs/llama_t8_final_optimized.json"
echo "📊 Dataset: train_set_corrected_90k.json"
echo "🔄 RESUME: checkpoint-7750 (step 7750 → 50000)"

# Training completo 24h - CONFIGURAZIONE TESTATA E FUNZIONANTE
python scripts/training/train_lora_multi_gpu_simple.py \
    --model_name_or_path meta-llama/Llama-3.1-8B-Instruct \
    --data_file data/processed/xml_format_optimized/train_set_corrected_90k.json \
    --config_path experiments/xml_direct_input/configs/llama_t8_final_optimized.json \
    --output_dir "$OUTPUT_DIR" \
    --disable_quantization \
    --use_wandb \
    --wandb_project svg_captioning \
    --wandb_run_name llama_t8_full_24h_leonardo \
    --resume_from_checkpoint "$OUTPUT_DIR/checkpoint-7750"

echo "🏁 LLAMA T8 TRAINING COMPLETO 24H COMPLETATO"
echo "End time: $(date)"
