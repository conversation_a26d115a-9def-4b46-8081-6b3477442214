#!/bin/bash
#SBATCH --job-name=GEMMA_T9_WORKING_COPY
#SBATCH --account=tesi_ediluzio
#SBATCH --output=logs/GEMMA_T9_WORKING_COPY_%j.out
#SBATCH --error=logs/GEMMA_T9_WORKING_COPY_%j.err
#SBATCH --partition=boost_usr_prod
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --gres=gpu:1
#SBATCH --mem=32G
#SBATCH --cpus-per-task=8
#SBATCH --qos=normal
#SBATCH --time=24:00:00

echo "🔥 GEMMA T9 WORKING CONFIG COPY - LEONARDO"
echo "Job ID: $SLURM_JOB_ID"
echo "Node: $SLURMD_NODENAME"
echo "Start time: $(date)"

cd /work/tesi_ediluzio
source $(conda info --base)/etc/profile.d/conda.sh
conda activate svg_env_new

# Configurazione che funzionava il 13 luglio
export HF_TOKEN="*************************************"
export HUGGINGFACE_HUB_TOKEN="*************************************"
export PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True,max_split_size_mb:512
export CUDA_LAUNCH_BLOCKING=1
export WANDB_PROJECT=svg_captioning
export WANDB_RUN_NAME=gemma_t9_working_copy_leonardo
export TOKENIZERS_PARALLELISM=false
export HF_HOME=/work/tesi_ediluzio/.cache/huggingface

echo "✅ Python: $(which python)"
echo "✅ CUDA available: $(python -c 'import torch; print(torch.cuda.is_available())')"

echo "✅ Avvio GEMMA T9 WORKING CONFIG COPY..."
echo "📁 Output: experiments/xml_direct_input/outputs/gemma_t9_working_copy"
echo "🔧 Config: experiments/xml_direct_input/configs/gemma_t9_gradient_accumulation_FIXED.json"
echo "📊 Dataset: train_set_corrected_90k.json (stesso del 13 luglio)"
echo "⚙️ SENZA quantizzazione (come funzionava)"

# Crea directory output
mkdir -p experiments/xml_direct_input/outputs/gemma_t9_working_copy

# Training SENZA quantizzazione come il 13 luglio
python scripts/training/train_lora_multi_gpu_simple.py \
    --model_name_or_path google/gemma-2-9b-it \
    --data_file data/processed/xml_format_optimized/train_set_corrected_90k.json \
    --config_path experiments/xml_direct_input/configs/gemma_t9_gradient_accumulation_FIXED.json \
    --output_dir experiments/xml_direct_input/outputs/gemma_t9_working_copy \
    --disable_quantization \
    --use_wandb \
    --wandb_project svg_captioning \
    --wandb_run_name gemma_t9_working_copy_leonardo

echo "🏁 GEMMA T9 WORKING CONFIG COPY COMPLETATO"
echo "End time: $(date)"
