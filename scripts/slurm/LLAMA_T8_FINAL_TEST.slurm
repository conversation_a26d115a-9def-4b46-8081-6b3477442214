#!/bin/bash
#SBATCH --job-name=LLAMA_T8_FINAL_TEST
#SBATCH --account=tesi_ediluzio
#SBATCH --output=logs/LLAMA_T8_FINAL_TEST_%j.out
#SBATCH --error=logs/LLAMA_T8_FINAL_TEST_%j.err
#SBATCH --partition=boost_usr_prod
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --gres=gpu:1
#SBATCH --mem=32G
#SBATCH --cpus-per-task=8
#SBATCH --qos=normal
#SBATCH --time=24:00:00

echo "🚀 LLAMA T8 FINAL TEST - LEONARDO"
echo "Job ID: $SLURM_JOB_ID"
echo "Node: $SLURMD_NODENAME"
echo "Start time: $(date)"

cd /work/tesi_ediluzio
source $(conda info --base)/etc/profile.d/conda.sh
conda activate svg_env_new

echo "✅ Python: $(which python)"
echo "✅ CUDA available: $(python -c 'import torch; print(torch.cuda.is_available())')"

export TOKENIZERS_PARALLELISM=false
export CUDA_LAUNCH_BLOCKING=1
export PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True,max_split_size_mb:512
export HF_HOME=/work/tesi_ediluzio/.cache/huggingface
export TRANSFORMERS_CACHE=/work/tesi_ediluzio/.cache/huggingface/transformers
export HUGGINGFACE_HUB_TOKEN=*************************************
export WANDB_PROJECT=svg_captioning
export WANDB_RUN_NAME=llama_t8_final_test

echo "✅ Avvio LLAMA T8 FINAL TEST..."
echo "📁 Output: experiments/xml_direct_input/outputs/llama_t8_24h"
echo "🔧 Config: experiments/xml_direct_input/configs/llama_t8_final_optimized.json"
echo "📊 Dataset: train_set_corrected_90k.json"
echo "🔄 RESUME: checkpoint-7750"

timeout 23h python scripts/training/train_lora_simple.py \
    --model_name_or_path meta-llama/Llama-3.1-8B-Instruct \
    --data_file data/processed/xml_format_optimized/train_set_corrected_90k.json \
    --config_path experiments/xml_direct_input/configs/llama_t8_final_optimized.json \
    --output_dir experiments/xml_direct_input/outputs/llama_t8_24h \
    --use_wandb \
    --wandb_project svg_captioning \
    --wandb_run_name llama_t8_final_test \
    --resume_from_checkpoint experiments/xml_direct_input/outputs/llama_t8_24h/checkpoint-7750

echo "🏁 LLAMA T8 FINAL TEST COMPLETATO"
echo "End time: $(date)"
