#!/bin/bash
#SBATCH --job-name=TEST_GEMMA_T9_6MIN
#SBATCH --account=tesi_ediluzio
#SBATCH --output=logs/TEST_GEMMA_T9_6MIN_%j.out
#SBATCH --error=logs/TEST_GEMMA_T9_6MIN_%j.err
#SBATCH --partition=boost_usr_prod
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --gres=gpu:1
#SBATCH --mem=32G
#SBATCH --cpus-per-task=8
#SBATCH --qos=normal
#SBATCH --time=00:06:00

echo "🧪 TEST GEMMA T9 - 6 MINUTI"
echo "Job ID: $SLURM_JOB_ID"
echo "Node: $SLURMD_NODENAME"
echo "Start time: $(date)"
echo "🆕 DA ZERO (checkpoint-5250 è vuoto)"

cd /work/tesi_ediluzio
source $(conda info --base)/etc/profile.d/conda.sh
conda activate svg_env_new

export HF_TOKEN="*************************************"
export HUGGINGFACE_HUB_TOKEN="*************************************"
export PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True,max_split_size_mb:512
export CUDA_LAUNCH_BLOCKING=1
export WANDB_PROJECT=svg_captioning
export WANDB_RUN_NAME=test_gemma_t9_6min_leonardo
export TOKENIZERS_PARALLELISM=false
export HF_HOME=/work/tesi_ediluzio/.cache/huggingface

OUTPUT_DIR="experiments/xml_direct_input/outputs/gemma_t9_fixed_leonardo"

echo "✅ Python: $(which python)"
echo "✅ CUDA available: $(python -c 'import torch; print(torch.cuda.is_available())')"
echo "✅ Avvio TEST GEMMA T9 da zero..."

timeout 5m python scripts/training/train_lora_multi_gpu_simple.py \
    --model_name_or_path google/gemma-2-9b-it \
    --data_file data/processed/xml_format_optimized/train_set_corrected_90k.json \
    --config_path experiments/xml_direct_input/configs/gemma_t9_gradient_accumulation_FIXED.json \
    --output_dir "$OUTPUT_DIR" \
    --disable_quantization \
    --use_wandb \
    --wandb_project svg_captioning \
    --wandb_run_name test_gemma_t9_6min_leonardo

echo "🏁 TEST GEMMA T9 6MIN COMPLETATO"
echo "End time: $(date)"
