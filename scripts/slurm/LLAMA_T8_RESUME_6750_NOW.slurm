#!/bin/bash
#SBATCH --job-name=LLAMA_T8_RESUME_6750_NOW
#SBATCH --account=tesi_ediluzio
#SBATCH --output=logs/LLAMA_T8_RESUME_6750_NOW_%j.out
#SBATCH --error=logs/LLAMA_T8_RESUME_6750_NOW_%j.err
#SBATCH --partition=boost_usr_prod
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --gres=gpu:1
#SBATCH --mem=32G
#SBATCH --cpus-per-task=8
#SBATCH --qos=normal
#SBATCH --time=24:00:00

echo "🚀 LLAMA T8 TRAINING 24H - LEONARDO"
echo "Job ID: $SLURM_JOB_ID"
echo "Node: $SLURMD_NODENAME"
echo "Start time: $(date)"
echo "🔄 RESUME: checkpoint-6750 (ULTIMO CHECKPOINT)"

# Environment setup
cd /work/tesi_ediluzio
source $(conda info --base)/etc/profile.d/conda.sh
conda activate svg_env_new

# Configura token HuggingFace
export HF_TOKEN="*************************************"
export HUGGINGFACE_HUB_TOKEN="*************************************"

# Configurazione memoria CUDA OTTIMIZZATA
export PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True,max_split_size_mb:512
export CUDA_LAUNCH_BLOCKING=1
export PYTORCH_CUDA_ALLOC_CONF=garbage_collection_threshold:0.6,max_split_size_mb:512

# Environment variables
export WANDB_PROJECT=svg_captioning
export WANDB_RUN_NAME=llama_t8_resume_6750_now_leonardo
export TOKENIZERS_PARALLELISM=false
export HUGGINGFACE_HUB_CACHE=/work/tesi_ediluzio/.cache/huggingface
export HF_HOME=/work/tesi_ediluzio/.cache/huggingface

# Output directory (dove sono i checkpoint)
OUTPUT_DIR="experiments/xml_direct_input/outputs/llama_t8_24h"

echo "✅ Avvio LLAMA T8 RESUME 6750 NOW..."
echo "📁 Output: $OUTPUT_DIR"
echo "🔧 Config: experiments/xml_direct_input/configs/llama_t8_final_optimized.json"
echo "📊 Dataset: train_set_corrected_90k.json"
echo "🔄 RESUME AUTOMATICO: Trova checkpoint-6750"

# Training con RESUME AUTOMATICO
python scripts/training/train_lora_multi_gpu_simple.py \
    --model_name_or_path meta-llama/Llama-3.1-8B-Instruct \
    --data_file data/processed/xml_format_optimized/train_set_corrected_90k.json \
    --config_path experiments/xml_direct_input/configs/llama_t8_final_optimized.json \
    --output_dir "$OUTPUT_DIR" \
    --disable_quantization \
    --use_wandb \
    --wandb_project svg_captioning \
    --wandb_run_name llama_t8_resume_6750_now_leonardo

echo "🏁 LLAMA T8 RESUME 6750 NOW COMPLETATO"
echo "End time: $(date)"
