#!/bin/bash
#SBATCH --job-name=GEMMA_T9_WORKING
#SBATCH --account=tesi_ediluzio
#SBATCH --output=logs/GEMMA_T9_WORKING_%j.out
#SBATCH --error=logs/GEMMA_T9_WORKING_%j.err
#SBATCH --partition=boost_usr_prod
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --gres=gpu:2
#SBATCH --mem=64G
#SBATCH --cpus-per-task=16
#SBATCH --qos=normal
#SBATCH --time=24:00:00

echo "🚀 GEMMA T9 WORKING SCRIPT (QUELLO CHE FUNZIONAVA)"
echo "Job ID: $SLURM_JOB_ID"
echo "Node: $SLURMD_NODENAME"
echo "Start time: $(date)"
echo "🎯 Configurazione: Gradient Accumulation = 8"
echo "🎯 LoRA: Rank 64, Alpha 128"
echo "🎯 Quantization: DISABLED"

# Environment setup
cd /work/tesi_ediluzio
source $(conda info --base)/etc/profile.d/conda.sh
conda activate svg_env_new

# Verifica ambiente
echo "🔧 Verifico ambiente..."
python -c "import transformers; print(f'✅ Transformers: {transformers.__version__}')"
python -c "import accelerate; print(f'✅ Accelerate: {accelerate.__version__}')"
python -c "import peft; print(f'✅ PEFT: {peft.__version__}')"
python -c "import torch; print(f'✅ CUDA available: {torch.cuda.is_available()}, GPUs: {torch.cuda.device_count()}')"

# Fix PEFT bug se necessario
echo "🔧 Applicando fix PEFT active_adapters..."
python -c "
import site
import os
trainer_path = os.path.join(site.getsitepackages()[0], 'transformers', 'trainer.py')
if os.path.exists(trainer_path):
    with open(trainer_path, 'r') as f:
        content = f.read()
    if 'if len(active_adapters) > 1:' in content:
        content = content.replace(
            'if len(active_adapters) > 1:',
            'if hasattr(active_adapters, \"__len__\") and len(active_adapters) > 1:'
        )
        with open(trainer_path, 'w') as f:
            f.write(content)
        print('✅ Fix PEFT applicato')
    else:
        print('✅ Fix PEFT già presente o non necessario')
else:
    print('⚠️ trainer.py non trovato')
"

# Environment variables
export WANDB_PROJECT=svg_captioning
export WANDB_RUN_NAME=gemma_t9_working_script
export TOKENIZERS_PARALLELISM=false
export CUDA_LAUNCH_BLOCKING=0
export HUGGINGFACE_HUB_CACHE=/work/tesi_ediluzio/.cache/huggingface
export HF_HOME=/work/tesi_ediluzio/.cache/huggingface
export HUGGINGFACE_HUB_TOKEN=*************************************

# Output directory
OUTPUT_DIR="experiments/xml_direct_input/outputs/gemma_t9_working"
mkdir -p "$OUTPUT_DIR"
mkdir -p logs

echo "✅ Avvio GEMMA T9 WORKING SCRIPT..."
echo "📁 Output directory: $OUTPUT_DIR"
echo "🔧 Config: experiments/xml_direct_input/configs/gemma_t9_gradient_accumulation.json"

# Training con gradient accumulation (SCRIPT ORIGINALE CHE FUNZIONAVA)
/homes/ediluzio/.conda/envs/svg_env_new/bin/python -m torch.distributed.run \
    --nproc_per_node=2 \
    --master_port=29500 \
    scripts/training/train_lora_multi_gpu_simple.py \
    --model_name_or_path google/gemma-2-9b-it \
    --data_file data/processed/xml_format_optimized/train_set_corrected_90k.json \
    --config_path experiments/xml_direct_input/configs/gemma_t9_gradient_accumulation.json \
    --output_dir "$OUTPUT_DIR" \
    --disable_quantization \
    --use_wandb \
    --wandb_project svg_captioning \
    --wandb_run_name gemma_t9_working_script

echo "🏁 GEMMA T9 WORKING SCRIPT COMPLETATO"
echo "End time: $(date)"
echo "📁 Checkpoint salvati in: $OUTPUT_DIR"
