#!/bin/bash
#SBATCH --job-name=GEMMA_T9_RESUME_5250_NOW
#SBATCH --account=tesi_ediluzio
#SBATCH --output=logs/GEMMA_T9_RESUME_5250_NOW_%j.out
#SBATCH --error=logs/GEMMA_T9_RESUME_5250_NOW_%j.err
#SBATCH --partition=boost_usr_prod
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --gres=gpu:1
#SBATCH --mem=32G
#SBATCH --cpus-per-task=8
#SBATCH --qos=normal
#SBATCH --time=24:00:00

echo "🚀 GEMMA T9 TRAINING 24H - LEONARDO"
echo "Job ID: $SLURM_JOB_ID"
echo "Node: $SLURMD_NODENAME"
echo "Start time: $(date)"
echo "🔄 RESUME: checkpoint-5250 (ULTIMO CHECKPOINT)"

# Moduli
module load python/3.11.6
module load cuda/12.1

# Ambiente virtuale
source venv/bin/activate

# Configurazione memoria CUDA OTTIMIZZATA
export PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True,max_split_size_mb:512
export CUDA_LAUNCH_BLOCKING=1
export PYTORCH_CUDA_ALLOC_CONF=garbage_collection_threshold:0.6,max_split_size_mb:512

# Configurazione CUDA
export CUDA_VISIBLE_DEVICES=0

# Configurazione HuggingFace
export HF_HOME=/work/tesi_ediluzio/.cache/huggingface
export TRANSFORMERS_CACHE=/work/tesi_ediluzio/.cache/huggingface/transformers

echo "✅ Avvio GEMMA T9 RESUME 5250 NOW..."
echo "📁 Output: experiments/xml_direct_input/outputs/gemma_t9_fixed_leonardo"
echo "🔧 Config: experiments/xml_direct_input/configs/gemma_t9_gradient_accumulation_FIXED.json"
echo "📊 Dataset: train_set_corrected_90k.json"
echo "🔄 RESUME AUTOMATICO: Trova checkpoint-5250"

# Lancio training con resume automatico da checkpoint-5250
python scripts/training/train_lora_multi_gpu_simple.py \
    --config experiments/xml_direct_input/configs/gemma_t9_gradient_accumulation_FIXED.json \
    --disable_quantization \
    --use_wandb \
    --resume_from_checkpoint experiments/xml_direct_input/outputs/gemma_t9_fixed_leonardo/checkpoint-5250

echo "🏁 GEMMA T9 RESUME 5250 NOW COMPLETATO"
echo "End time: $(date)"
