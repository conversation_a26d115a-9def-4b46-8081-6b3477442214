_comment:
    value: LLAMA T8 - Configurazione Completa Finale Ottimizzata DA ZERO
_comment_batch:
    value: Batch configuration IDENTICA a Gemma T7 - dual-GPU
_comment_dataset:
    value: 'Dataset: 90K training + 10K validation examples from 100K SVG-caption pairs'
_comment_distributed:
    value: Distributed training configuration IDENTICA a Gemma T7 - dual-GPU
_comment_early_stopping:
    value: Early stopping IDENTICA a Gemma T7
_comment_evaluation:
    value: Evaluation and saving strategy IDENTICA a Gemma T7
_comment_evaluation_metrics:
    value: Evaluation metrics IDENTICA a Gemma T7
_comment_final:
    value: Configuration IDENTICA a Gemma T7 per confronto equo tra Llama-3.1-8B e Gemma-2-9B
_comment_generation:
    value: Generation parameters IDENTICA a Gemma T7
_comment_hardware:
    value: Hardware specifications IDENTICA a Gemma T7
_comment_logging:
    value: Logging e monitoring configuration
_comment_lora:
    value: LoRA configuration IDENTICA a Gemma T7
_comment_memory:
    value: Memory optimization settings IDENTICA a Gemma T7
_comment_model_specs:
    value: Model architecture specifications - Llama-3.1-8B
_comment_optimizer:
    value: Optimizer configuration IDENTICA a Gemma T7
_comment_performance:
    value: Performance targets IDENTICA a Gemma T7
_comment_precision:
    value: Mixed precision training IDENTICA a Gemma T7
_comment_prompt_format:
    value: Llama-3.1 specific prompt format
_comment_quantization:
    value: 4-bit quantization IDENTICA a Gemma T7
_comment_reproducibility:
    value: Reproducibility settings IDENTICA a Gemma T7
_comment_training:
    value: Training configuration IDENTICA a Gemma T7 - dual-GPU
_comment_training_data:
    value: Training data specifications IDENTICA a Gemma T7
_created:
    value: "2025-06-18"
_description:
    value: Training finale Llama-3.1-8B-Instruct per SVG captioning con quantizzazione 4-bit, LoRA ottimizzato e dual-GPU DA ZERO
_name_or_path:
    value: meta-llama/Llama-3.1-8B-Instruct
_version:
    value: T8.0
_wandb:
    value:
        cli_version: 0.20.1
        m:
            - "1": train/global_step
              "6":
                - 3
              "7": []
        python_version: 3.10.18
        t:
            "1":
                - 1
                - 5
                - 11
                - 41
                - 49
                - 51
                - 53
                - 71
                - 98
            "2":
                - 1
                - 5
                - 11
                - 41
                - 49
                - 51
                - 53
                - 63
                - 71
                - 98
            "3":
                - 7
                - 13
                - 16
                - 19
                - 55
                - 66
            "4": 3.10.18
            "5": 0.20.1
            "6": 4.53.1
            "9":
                "1": transformers_trainer
            "12": 0.20.1
            "13": linux-x86_64
accelerator_config:
    value:
        dispatch_batches: null
        even_batches: true
        gradient_accumulation_kwargs: null
        non_blocking: false
        split_batches: false
        use_seedable_sampler: true
adafactor:
    value: false
adam_beta1:
    value: 0.9
adam_beta2:
    value: 0.999
adam_epsilon:
    value: 1e-08
add_cross_attention:
    value: false
architectures:
    value:
        - LlamaForCausalLM
attention_bias:
    value: false
attention_dropout:
    value: 0
auto_find_batch_size:
    value: false
average_tokens_across_devices:
    value: false
bad_words_ids:
    value: null
batch_eval_metrics:
    value: false
begin_suppress_tokens:
    value: null
bf16:
    value: false
bf16_full_eval:
    value: false
bnb_4bit_compute_dtype:
    value: float16
bnb_4bit_quant_storage:
    value: uint8
bnb_4bit_quant_type:
    value: nf4
bnb_4bit_use_double_quant:
    value: true
bos_token_id:
    value: 128000
checkpoint_frequency_minutes:
    value: 60
chunk_size_feed_forward:
    value: 0
context_length:
    value: 131072
cpu_cores:
    value: 16
cpu_offload:
    value: false
cross_attention_hidden_size:
    value: null
data_file:
    value: data/processed/xml_format_optimized/train_set_corrected_90k.json
data_seed:
    value: null
dataloader_drop_last:
    value: false
dataloader_num_workers:
    value: 2
dataloader_persistent_workers:
    value: false
dataloader_pin_memory:
    value: true
dataloader_prefetch_factor:
    value: null
dataset_size_train:
    value: 90000
dataset_size_val:
    value: 10000
ddp_backend:
    value: null
ddp_broadcast_buffers:
    value: null
ddp_bucket_cap_mb:
    value: null
ddp_find_unused_parameters:
    value: null
ddp_timeout:
    value: 1800
debug:
    value: []
decoder_start_token_id:
    value: null
deepspeed:
    value: null
disable_tqdm:
    value: false
diversity_penalty:
    value: 0
do_eval:
    value: false
do_predict:
    value: false
do_sample:
    value: false
do_train:
    value: false
early_stopping:
    value: false
early_stopping_patience:
    value: 15
early_stopping_threshold:
    value: 0.0005
effective_batch_size:
    value: 16
encoder_no_repeat_ngram_size:
    value: 0
eos_token:
    value: <|eot_id|>
eos_token_id:
    value:
        - 128001
        - 128008
        - 128009
estimated_steps_per_hour:
    value: 2083
estimated_total_time_hours:
    value: 24
eval_accumulation_steps:
    value: null
eval_delay:
    value: 0
eval_do_concat_batches:
    value: true
eval_metrics:
    value:
        - loss
        - perplexity
        - bleu4
        - cider
        - rouge_l
        - clip_score
eval_on_start:
    value: false
eval_steps:
    value: null
eval_strategy:
    value: "no"
eval_use_gather_object:
    value: false
evaluation_strategy:
    value: steps
exponential_decay_length_penalty:
    value: null
finetuning_task:
    value: null
forced_bos_token_id:
    value: null
forced_eos_token_id:
    value: null
fp16:
    value: true
fp16_backend:
    value: auto
fp16_full_eval:
    value: false
fp16_opt_level:
    value: O1
fsdp:
    value: []
fsdp_config:
    value:
        min_num_params: 0
        xla: false
        xla_fsdp_grad_ckpt: false
        xla_fsdp_v2: false
fsdp_min_num_params:
    value: 0
fsdp_transformer_layer_cls_to_wrap:
    value: null
full_determinism:
    value: false
generation_do_sample:
    value: true
generation_max_length:
    value: 150
generation_num_beams:
    value: 1
generation_repetition_penalty:
    value: 1.1
generation_temperature:
    value: 0.7
generation_top_k:
    value: 50
generation_top_p:
    value: 0.9
gpu_memory_gb:
    value: 48
gradient_accumulation_steps:
    value: 8
gradient_checkpointing:
    value: false
gradient_checkpointing_kwargs:
    value: null
greater_is_better:
    value: null
group_by_length:
    value: false
half_precision_backend:
    value: auto
head_dim:
    value: 128
hidden_act:
    value: silu
hidden_size:
    value: 4096
hub_always_push:
    value: false
hub_model_id:
    value: null
hub_private_repo:
    value: null
hub_revision:
    value: null
hub_strategy:
    value: every_save
hub_token:
    value: <HUB_TOKEN>
id2label:
    value:
        "0": LABEL_0
        "1": LABEL_1
ignore_data_skip:
    value: false
include_for_metrics:
    value: []
include_inputs_for_metrics:
    value: false
include_num_input_tokens_seen:
    value: false
include_tokens_per_second:
    value: false
initializer_range:
    value: 0.02
input_format:
    value: SVG XML
intermediate_size:
    value: 14336
is_decoder:
    value: false
is_encoder_decoder:
    value: false
jit_mode_eval:
    value: false
label_names:
    value: null
label_smoothing_factor:
    value: 0
label2id:
    value:
        LABEL_0: 0
        LABEL_1: 1
learning_rate:
    value: 5e-05
length_column_name:
    value: length
length_penalty:
    value: 1
liger_kernel_config:
    value: null
load_best_model_at_end:
    value: false
load_in_4bit:
    value: true
local_rank:
    value: 0
log_level:
    value: passive
log_level_replica:
    value: warning
log_on_each_node:
    value: true
logging_dir:
    value: experiments/xml_direct_input/outputs/llama_t8_24h/runs/Jul16_17-56-55_franco
logging_first_step:
    value: false
logging_nan_inf_filter:
    value: true
logging_steps:
    value: 25
logging_strategy:
    value: steps
lora_alpha:
    value: 128
lora_bias:
    value: none
lora_dropout:
    value: 0.05
lora_r:
    value: 64
lora_target_modules:
    value:
        - q_proj
        - k_proj
        - v_proj
        - o_proj
        - gate_proj
        - up_proj
        - down_proj
lora_task_type:
    value: CAUSAL_LM
lr_scheduler_type:
    value: linear
max_caption_length:
    value: 200
max_eval_samples:
    value: 500
max_grad_norm:
    value: 1
max_length:
    value: 20
max_memory_MB:
    value: 46000
max_position_embeddings:
    value: 131072
max_steps:
    value: 50000
max_svg_length:
    value: 1800
metric_for_best_model:
    value: null
min_length:
    value: 0
mlp_bias:
    value: false
model/num_parameters:
    value: 8198033408
model_architecture:
    value: Llama-3.1
model_name_or_path:
    value: meta-llama/Llama-3.1-8B-Instruct
model_size_parameters:
    value: 8B
model_type:
    value: llama
mp_parameters:
    value: ""
neftune_noise_alpha:
    value: null
no_cuda:
    value: false
no_repeat_ngram_size:
    value: 0
num_attention_heads:
    value: 32
num_beam_groups:
    value: 1
num_beams:
    value: 1
num_gpus:
    value: 2
num_hidden_layers:
    value: 32
num_key_value_heads:
    value: 8
num_layers:
    value: 32
num_return_sequences:
    value: 1
num_train_epochs:
    value: 3
optim:
    value: adamw_torch
optim_args:
    value: null
optim_target_modules:
    value: null
output_attentions:
    value: false
output_dir:
    value: experiments/xml_direct_input/outputs/llama_t8_24h
output_format:
    value: Natural language caption
output_hidden_states:
    value: false
output_scores:
    value: false
overwrite_output_dir:
    value: false
pad_token:
    value: <|end_of_text|>
pad_token_id:
    value: null
past_index:
    value: -1
peft_config:
    value:
        default:
            auto_mapping: null
            base_model_name_or_path: meta-llama/Llama-3.1-8B-Instruct
            bias: none
            corda_config: null
            eva_config: null
            exclude_modules: null
            fan_in_fan_out: false
            inference_mode: false
            init_lora_weights: true
            layer_replication: null
            layers_pattern: null
            layers_to_transform: null
            lora_alpha: 128
            lora_bias: false
            lora_dropout: 0.05
            megatron_config: null
            megatron_core: megatron.core
            modules_to_save: null
            peft_type: LORA
            qalora_group_size: 16
            r: 64
            revision: null
            runtime_config:
                ephemeral_gpu_offload: false
            target_modules:
                - q_proj
                - k_proj
                - v_proj
                - o_proj
                - up_proj
                - down_proj
                - gate_proj
            task_type: CAUSAL_LM
            trainable_token_indices: null
            use_dora: false
            use_qalora: false
            use_rslora: false
per_device_eval_batch_size:
    value: 8
per_device_train_batch_size:
    value: 1
per_gpu_eval_batch_size:
    value: null
per_gpu_train_batch_size:
    value: null
pin_memory:
    value: true
prediction_loss_only:
    value: false
prefix:
    value: null
preprocessing:
    value: XML format optimized
pretraining_tp:
    value: 1
problem_type:
    value: null
prompt_template:
    value: |+
        <|begin_of_text|><|start_header_id|>user<|end_header_id|>

        Descrivi questa immagine SVG:
        {svg_content}<|eot_id|><|start_header_id|>assistant<|end_header_id|>

push_to_hub:
    value: false
push_to_hub_model_id:
    value: null
push_to_hub_organization:
    value: null
push_to_hub_token:
    value: <PUSH_TO_HUB_TOKEN>
quantization_config:
    value:
        _load_in_4bit: true
        _load_in_8bit: false
        bnb_4bit_compute_dtype: float16
        bnb_4bit_quant_storage: uint8
        bnb_4bit_quant_type: nf4
        bnb_4bit_use_double_quant: true
        llm_int8_enable_fp32_cpu_offload: false
        llm_int8_has_fp16_weight: false
        llm_int8_skip_modules: null
        llm_int8_threshold: 6
        load_in_4bit: true
        load_in_8bit: false
        quant_method: BITS_AND_BYTES
ram_gb:
    value: 64
ray_scope:
    value: last
remove_invalid_values:
    value: false
remove_unused_columns:
    value: false
repetition_penalty:
    value: 1
report_to:
    value:
        - wandb
restore_callback_states_from_checkpoint:
    value: false
resume_from_checkpoint:
    value: null
return_dict:
    value: true
return_dict_in_generate:
    value: false
rms_norm_eps:
    value: 1e-05
rope_scaling:
    value:
        factor: 8
        high_freq_factor: 4
        low_freq_factor: 1
        original_max_position_embeddings: 8192
        rope_type: llama3
rope_theta:
    value: 500000
run_name:
    value: llama_t8_final_test
save_on_each_node:
    value: false
save_only_model:
    value: false
save_safetensors:
    value: true
save_steps:
    value: 250
save_strategy:
    value: steps
save_total_limit:
    value: null
seed:
    value: 42
sep_token_id:
    value: null
skip_memory_metrics:
    value: true
suppress_tokens:
    value: null
target_gpu:
    value: L40S
target_training_time_hours:
    value: 24
task_specific_params:
    value: null
task_type:
    value: svg_to_caption
temperature:
    value: 1
tf_legacy_loss:
    value: false
tf32:
    value: null
tie_encoder_decoder:
    value: false
tie_word_embeddings:
    value: false
tokenizer_class:
    value: null
top_k:
    value: 50
top_p:
    value: 1
torch_compile:
    value: false
torch_compile_backend:
    value: null
torch_compile_mode:
    value: null
torch_dtype:
    value: float16
torch_empty_cache_steps:
    value: null
torchdynamo:
    value: null
torchscript:
    value: false
tpu_metrics_debug:
    value: false
tpu_num_cores:
    value: null
transformers_seed:
    value: 42
transformers_version:
    value: 4.53.1
typical_p:
    value: 1
use_bfloat16:
    value: false
use_cache:
    value: true
use_cpu:
    value: false
use_ipex:
    value: false
use_legacy_prediction_loop:
    value: false
use_liger_kernel:
    value: false
use_mps_device:
    value: false
val_file:
    value: data/processed/xml_format_optimized/test_set_corrected_10k.json
vocab_size:
    value: 128256
warmup_ratio:
    value: 0
warmup_steps:
    value: 1000
weight_decay:
    value: 0
