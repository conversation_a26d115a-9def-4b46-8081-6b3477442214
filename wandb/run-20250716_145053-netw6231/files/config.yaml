_wandb:
    value:
        cli_version: 0.20.1
        m: []
        python_version: 3.10.18
        t:
            "1":
                - 1
                - 5
                - 11
                - 41
                - 49
                - 51
                - 53
                - 71
                - 98
            "2":
                - 1
                - 5
                - 11
                - 41
                - 49
                - 51
                - 53
                - 71
                - 98
            "3":
                - 13
                - 16
                - 55
            "4": 3.10.18
            "5": 0.20.1
            "6": 4.53.1
            "12": 0.20.1
            "13": linux-x86_64
bf16:
    value: false
bnb_4bit_compute_dtype:
    value: float16
bnb_4bit_quant_type:
    value: nf4
bnb_4bit_use_double_quant:
    value: true
data_file:
    value: data/processed/xml_format_optimized/train_set_corrected_90k.json
dataloader_num_workers:
    value: 4
do_eval:
    value: true
do_train:
    value: true
eval_steps:
    value: 250
evaluation_strategy:
    value: steps
fp16:
    value: true
gradient_accumulation_steps:
    value: 8
greater_is_better:
    value: false
learning_rate:
    value: 5e-05
load_best_model_at_end:
    value: true
load_in_4bit:
    value: false
logging_steps:
    value: 25
lora_alpha:
    value: 128
lora_bias:
    value: none
lora_dropout:
    value: 0.05
lora_r:
    value: 64
lora_target_modules:
    value:
        - q_proj
        - k_proj
        - v_proj
        - o_proj
        - gate_proj
        - up_proj
        - down_proj
lr_scheduler_type:
    value: cosine
max_eval_samples:
    value: 500
max_length:
    value: 2048
max_steps:
    value: 50000
metric_for_best_model:
    value: eval_loss
model_name_or_path:
    value: google/gemma-2-9b-it
num_train_epochs:
    value: 3
output_dir:
    value: experiments/xml_direct_input/outputs/gemma_t9_gradient_accumulation
overwrite_output_dir:
    value: true
per_device_eval_batch_size:
    value: 1
per_device_train_batch_size:
    value: 1
remove_unused_columns:
    value: false
save_steps:
    value: 250
save_strategy:
    value: steps
save_total_limit:
    value: 3
task_type:
    value: CAUSAL_LM
validation_file:
    value: data/processed/xml_format_optimized/test_set_corrected_10k.json
warmup_steps:
    value: 1000
weight_decay:
    value: 0.01
