#!/bin/bash
#SBATCH --job-name=gemma_t9_fresh
#SBATCH --account=tesi_ediluzio
#SBATCH --partition=all_usr_prod
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --cpus-per-task=8
#SBATCH --mem=64G
#SBATCH --gres=gpu:1
#SBATCH --time=24:00:00
#SBATCH --output=logs/gemma_t9_fresh_%j.out
#SBATCH --error=logs/gemma_t9_fresh_%j.err

echo "🔥 GEMMA T9 FRESH START DA CAPO"
echo "Job ID: $SLURM_JOB_ID"
echo "Node: $SLURMD_NODENAME"
echo "GPU: $CUDA_VISIBLE_DEVICES"

cd /work/tesi_ediluzio

# Attiva ambiente
source ~/.bashrc
eval "$(conda shell.bash hook)"
conda activate tesi_env

# Variabili
MODEL_NAME="google/gemma-2-9b-it"
OUTPUT_DIR="experiments/xml_direct_input/outputs/gemma_t9_fresh_$(date +%Y%m%d_%H%M%S)"

echo "📁 Output: $OUTPUT_DIR"
echo "🆕 Fresh start - no resume"

# Crea directory
mkdir -p $OUTPUT_DIR
mkdir -p logs

# Training da capo
python -m accelerate.commands.launch \
    --config_file experiments/xml_direct_input/configs/accelerate_config.yaml \
    experiments/xml_direct_input/train_xml_direct.py \
    --model_name_or_path $MODEL_NAME \
    --dataset_path data/processed/xml_direct_input_dataset.json \
    --output_dir $OUTPUT_DIR \
    --num_train_epochs 3 \
    --per_device_train_batch_size 1 \
    --gradient_accumulation_steps 16 \
    --learning_rate 2e-4 \
    --warmup_ratio 0.1 \
    --logging_steps 10 \
    --save_steps 500 \
    --eval_steps 500 \
    --save_total_limit 3 \
    --dataloader_num_workers 4 \
    --remove_unused_columns false \
    --report_to wandb \
    --run_name "gemma_t9_fresh_$(date +%Y%m%d_%H%M%S)" \
    --bf16 true \
    --gradient_checkpointing true \
    --use_lora true \
    --lora_r 16 \
    --lora_alpha 32 \
    --lora_dropout 0.1

echo "✅ GEMMA T9 FRESH COMPLETATO!"
