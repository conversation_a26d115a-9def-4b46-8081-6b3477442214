#!/bin/bash
#SBATCH --job-name=gemma_t9_fresh
#SBATCH --account=tesi_ediluzio
#SBATCH --partition=all_usr_prod
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --cpus-per-task=8
#SBATCH --mem=64G
#SBATCH --gres=gpu:1
#SBATCH --time=24:00:00
#SBATCH --output=logs/gemma_t9_fresh_%j.out
#SBATCH --error=logs/gemma_t9_fresh_%j.err

echo "🔥 GEMMA T9 FRESH START DA CAPO"
echo "Job ID: $SLURM_JOB_ID"
echo "Node: $SLURMD_NODENAME"
echo "GPU: $CUDA_VISIBLE_DEVICES"

cd /work/tesi_ediluzio

# Attiva ambiente IDENTICO A LLAMA
source $(conda info --base)/etc/profile.d/conda.sh
conda activate svg_env_new

# Variabili IDENTICHE A LLAMA
MODEL_NAME="google/gemma-2-9b-it"
OUTPUT_DIR="experiments/xml_direct_input/outputs/gemma_t9_fresh_$(date +%Y%m%d_%H%M%S)"

echo "📁 Output: $OUTPUT_DIR"
echo "🆕 Fresh start - no resume"
echo "🔧 Config: experiments/xml_direct_input/configs/llama_t8_final_optimized.json"
echo "📊 Dataset: train_set_corrected_90k.json"

# Crea directory
mkdir -p $OUTPUT_DIR
mkdir -p logs

# Training GEMMA con QUANTIZZAZIONE 4-bit (per GPU piccole)
python scripts/training/train_lora_simple.py \
    --model_name_or_path $MODEL_NAME \
    --data_file data/processed/xml_format_optimized/train_set_corrected_90k.json \
    --config_path experiments/xml_direct_input/configs/llama_t8_final_optimized.json \
    --output_dir $OUTPUT_DIR \
    --use_wandb \
    --wandb_project svg_captioning \
    --wandb_run_name "gemma_t9_fresh_quantized_$(date +%Y%m%d_%H%M%S)"

echo "✅ GEMMA T9 FRESH COMPLETATO!"
