#!/bin/bash
#SBATCH --job-name=llama_t8_resume
#SBATCH --partition=gpu
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --cpus-per-task=8
#SBATCH --mem=64G
#SBATCH --gres=gpu:1
#SBATCH --time=24:00:00
#SBATCH --output=logs/llama_t8_resume_%j.out
#SBATCH --error=logs/llama_t8_resume_%j.err

echo "🔥 LLAMA T8 RESUME DA CHECKPOINT-10250"
echo "Job ID: $SLURM_JOB_ID"
echo "Node: $SLURMD_NODENAME"
echo "GPU: $CUDA_VISIBLE_DEVICES"

cd /work/tesi_ediluzio

# Attiva ambiente
source ~/.bashrc
conda activate tesi_env

# Variabili
MODEL_NAME="meta-llama/Llama-3.1-8B-Instruct"
OUTPUT_DIR="experiments/xml_direct_input/outputs/llama_t8_resume_$(date +%Y%m%d_%H%M%S)"
RESUME_FROM="experiments/xml_direct_input/outputs/llama_t8_24h/checkpoint-10250"

echo "📁 Output: $OUTPUT_DIR"
echo "🔄 Resume from: $RESUME_FROM"

# Crea directory
mkdir -p $OUTPUT_DIR
mkdir -p logs

# Training con resume
python -m accelerate.commands.launch \
    --config_file experiments/xml_direct_input/configs/accelerate_config.yaml \
    experiments/xml_direct_input/train_xml_direct.py \
    --model_name_or_path $MODEL_NAME \
    --dataset_path data/processed/xml_direct_input_dataset.json \
    --output_dir $OUTPUT_DIR \
    --resume_from_checkpoint $RESUME_FROM \
    --num_train_epochs 3 \
    --per_device_train_batch_size 1 \
    --gradient_accumulation_steps 8 \
    --learning_rate 2e-4 \
    --warmup_ratio 0.1 \
    --logging_steps 10 \
    --save_steps 500 \
    --eval_steps 500 \
    --save_total_limit 3 \
    --dataloader_num_workers 4 \
    --remove_unused_columns false \
    --report_to wandb \
    --run_name "llama_t8_resume_$(date +%Y%m%d_%H%M%S)" \
    --bf16 true \
    --gradient_checkpointing true \
    --use_lora true \
    --lora_r 16 \
    --lora_alpha 32 \
    --lora_dropout 0.1

echo "✅ LLAMA T8 RESUME COMPLETATO!"
